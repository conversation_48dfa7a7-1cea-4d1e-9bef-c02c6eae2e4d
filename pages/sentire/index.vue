<script setup lang="ts">
import UserQuestion from "@/modules/sentire/components/UserQuestion.vue";
import UnderstandingPhase from "@/modules/sentire/components/UnderstandingPhase.vue";
import UserQuestionInput from "@/modules/sentire/components/UserQuestionInput.vue";
import HistoryAnalysis from "~/modules/sentire/components/HistoryAnalysis/index.vue";
import DeepAnalysisPhase from "@/modules/sentire/components/DeepAnalysisPhase.vue";
import { useCurrentThreadStore } from "~/modules/sentire/stores/currentThreadStore";
import { onMounted, onUnmounted } from "vue";
import Suggestions from "~/modules/sentire/components/Suggestions.vue";
import { useAutoScroll } from "@/modules/sentire/hooks/useAutoScroll";
import type { Suggestion, UserInput } from "~/modules/sentire/types";
import ToolBar from "~/modules/sentire/components/ToolBar.vue";
import { slugify } from "@/utils";
import { hiddenElementId } from "@/modules/sentire/helpers";
import Thinking from "@/modules/sentire/components/ThinkingIndicator.vue";
import PreviewArtifact from "@/modules/sentire/components/PreviewArtifact.vue";
import HistoryThreads from "~/modules/sentire/components/HistoryThreads.vue";
import { Threads } from "~/modules/sentire/models/threads";
import Spin from "@/components/Spin.vue";
import NewThread from "~/modules/sentire/components/NewThread.vue";
import { debounce } from "lodash-es";
import PlanTasks from "~/modules/sentire/components/PlanTasks.vue";
import { useScrollPadding } from "~/modules/sentire/hooks/useScrollPadding";
import ScrollToBottom from "~/modules/sentire/components/ScrollToBottom.vue";
import { nanoid } from "nanoid";

useHead({
  title: "Sentire",
});

const route = useRoute();
const router = useRouter();

const resourceId = useCookie("resourceId", {
  default: () => nanoid(4),
  watch: "shallow",
});

let threadId: string;

if (!route.query.threadId) {
  const threads = new Threads();
  const thread = await threads.createThread(
    undefined,
    undefined,
    resourceId.value
  );
  threadId = thread.id;
  if (import.meta.client) {
    console.log("create thread id in browser", thread.id);
    await router.replace({
      path: route.path,
      query: { ...route.query, threadId: thread.id },
    });
  } else {
    console.log("create thread id in server", thread.id);
    await navigateTo({
      path: route.path,
      query: { ...route.query, threadId: thread.id },
    });
  }
} else {
  console.log("use query thread id", route.query.threadId);
  threadId = route.query.threadId as string;
}
const inputRef = ref<typeof UserQuestionInput>();
const currentThreadStore = useCurrentThreadStore();
await currentThreadStore.initializeWithThreadId(threadId);

const {
  status,
  isPlanningPhase,
  isContentGenerationPhase,
  isFirstRun,
  isAgentAnalyzing,
  isRunning,
  isFirstPendingHistoryAnalyses,
  waitingForSuggestion,
  summary,
  artifactReport,
  historyAnalyses,
  finalContent,
} = storeToRefs(currentThreadStore);

const containerRef = ref<HTMLElement>();
const showLeftPanel = ref(true);
const analysisPanelWidth = ref();
const rightPanelRef = ref<HTMLElement>();

const { triggerScrollToBottom } = useAutoScroll(
  containerRef,
  isRunning,
  isContentGenerationPhase
);

const lastScrollTop = ref();

const updateLayoutRaw = () => {
  if (containerRef.value && artifactReport.value) {
    const containerWidth = containerRef.value.getBoundingClientRect().width;

    // 当有 artifactReport 时，右侧面板占 2/3 宽度，左侧占 1/3
    const rightPanelWidth = (containerWidth * 2) / 3;
    const leftPanelWidth = containerWidth - rightPanelWidth - 12 * 2;
    analysisPanelWidth.value = leftPanelWidth;

    // 当左侧宽度小于400px时，隐藏左侧面板，只显示报告
    if (leftPanelWidth < 400) {
      if (showLeftPanel.value) {
        lastScrollTop.value = window.scrollY;
      }
      showLeftPanel.value = false;
    } else {
      showLeftPanel.value = true;
    }
  }
};

// 防抖 200ms
const updateLayout = debounce(updateLayoutRaw, 200);

watch(showLeftPanel, (newVal) => {
  console.log(lastScrollTop.value, "lastScrollTop.value");

  if (newVal && lastScrollTop.value != null) {
    nextTick(() => {
      requestAnimationFrame(() => {
        window.scrollTo({ top: lastScrollTop.value! });
        lastScrollTop.value = null;
      });
    });
  }
});

let resizeObserver: ResizeObserver | null = null;

watch(artifactReport, () => {
  nextTick(() => {
    if (artifactReport.value && containerRef.value && !resizeObserver) {
      // 当报告出现时，开始监听容器大小变化
      resizeObserver = new ResizeObserver(() => {
        updateLayout();
      });
      resizeObserver.observe(containerRef.value);
      updateLayout();
    } else if (!artifactReport.value && resizeObserver) {
      resizeObserver.disconnect();
      resizeObserver = null;
      showLeftPanel.value = true;
      analysisPanelWidth.value = "";
    }
  });
});

const answerUserQuestion = async (userInput: UserInput) => {
  currentThreadStore.resetUnderstandingStepsCollapsed();
  triggerScrollToBottom("instant");
  await router.replace({
    path: route.path,
    query: {
      ...route.query,
      threadId: currentThreadStore.threadId,
    },
  });

  await currentThreadStore.startAnswerUserQuestion(userInput, async () => {
    await new Threads().updateThreadTopic(
      currentThreadStore.threadId,
      userInput.question
    );
  });
};

const handleSuggestionClicked = async (suggestion: Suggestion) => {
  const content = suggestion.content.replace(/`/g, "");
  await answerUserQuestion({ question: content, entities: [] });
};

const regenerateHistory = async (index: number, userInput: UserInput) => {
  currentThreadStore.resetUnderstandingStepsCollapsed();

  const historyAnalyses =
    index === -1
      ? currentThreadStore.historyAnalyses?.slice() ?? []
      : currentThreadStore.historyAnalyses?.slice(0, index) ?? [];
  const historyMessages = historyAnalyses
    .map((analysis) => {
      return analysis.messages;
    })
    .flat();

  currentThreadStore.cleanHistoryMessagesAfter(historyMessages.length);

  const threads = new Threads();
  await threads.reCreateThread(
    currentThreadStore.threadId,
    historyMessages,
    resourceId.value
  );

  await currentThreadStore.initializeWithThreadId(currentThreadStore.threadId);
  await answerUserQuestion(userInput);
};

const { scrollPaddingRef, updateScrollPadding } = useScrollPadding();

onMounted(async () => {
  const { pinwheel } = await import("ldrs");
  pinwheel.register();
});

onUnmounted(() => {
  if (import.meta.client) {
    if (resizeObserver) {
      resizeObserver.disconnect();
    }
  }
});
</script>

<template>
  <div
    v-if="isFirstPendingHistoryAnalyses"
    class="absolute left-1/2 top-1/2 z-50"
  >
    <Spin class="size-7" />
  </div>
  <div
    ref="containerRef"
    class="h-full w-full"
    :class="{
      flex: !!artifactReport,
      'p-4': !!artifactReport,
      'gap-3': !!artifactReport,
      'blur-sm': isFirstPendingHistoryAnalyses,
    }"
  >
    <div v-show="!artifactReport || showLeftPanel" class="flex-1 min-w-0">
      <div
        class="mx-auto relative"
        :class="{
          'flex-1 min-w-[400px]': !!artifactReport && showLeftPanel,
          'max-w-3xl xl:w-3xl': !artifactReport,
        }"
        :style="{
          paddingBottom: isRunning
            ? `${scrollPaddingRef - 32}px`
            : `${scrollPaddingRef}px`,
        }"
      >
        <ClientOnly>
          <Teleport to="#page-header-right">
            <div class="flex items-center">
              <HistoryThreads />
              <NewThread />
            </div>
          </Teleport>
        </ClientOnly>

        <HistoryAnalysis
          v-for="(analysis, index) in historyAnalyses"
          :key="index"
          :index="index"
          :analysis="analysis"
          @regenerate="regenerateHistory(index, analysis.userInput)"
        />

        <div
          v-if="currentThreadStore.userQuestion?.question"
          :id="`${slugify(currentThreadStore.userQuestion?.question ?? '')}`"
          :style="{
            minHeight: `calc(100vh - ${scrollPaddingRef}px)`,
          }"
        >
          <UserQuestion
            :value="currentThreadStore.userQuestion?.question ?? ''"
          />
          <!-- table of content anchor -->
          <div
            :id="`${slugify(
              hiddenElementId(currentThreadStore.userQuestion?.question ?? '')
            )}`"
          />

          <Thinking
            v-if="isRunning && !isPlanningPhase && !isContentGenerationPhase"
          />
          <UnderstandingPhase />

          <DeepAnalysisPhase>
            <template #default>
              <ToolBar
                v-if="status === 'done' || isAgentAnalyzing"
                class="relative -left-2"
                :copy-data="finalContent"
                :copy-show="status === 'done'"
                :status="status"
                @regenerate="
                  regenerateHistory(-1, currentThreadStore.userQuestion)
                "
              />
              <ClientOnly>
                <l-pinwheel
                  v-if="(isAgentAnalyzing && summary) || waitingForSuggestion"
                  size="20"
                  speed="1.3"
                  color="oklch(0.5613 0.0924 238.72)"
                  class="my-2"
                />
              </ClientOnly>
              <Suggestions @suggestion-clicked="handleSuggestionClicked" />
            </template>
          </DeepAnalysisPhase>
        </div>

        <div
          id="bottom-container"
          class="bg-white z-30"
          :class="[
            {
              'fixed bottom-0 pb-3':
                (historyAnalyses?.length ?? 0) !== 0 || !isFirstRun,
            },
          ]"
          :style="
            analysisPanelWidth
              ? { width: analysisPanelWidth + 'px' }
              : { width: '768px', maxWidth: '768px' }
          "
        >
          <div class="relative">
            <ScrollToBottom
              v-if="!isFirstRun"
              :is-running="isRunning"
              @to-bottom="triggerScrollToBottom"
            />
          </div>
          <PlanTasks @expand-changed="updateScrollPadding" />
          <UserQuestionInput
            ref="inputRef"
            :simple="(historyAnalyses?.length ?? 0) !== 0 || !isFirstRun"
            @search="(input) => answerUserQuestion(input)"
          />
        </div>
      </div>
    </div>
    <div
      v-if="!!artifactReport"
      ref="rightPanelRef"
      v-motion="{
        initial: { opacity: 0, transform: 'scale(0.5)' },
        enter: {
          opacity: 1,
          transform: 'scale(1)',
        },
      }"
      class="w-2/3 h-[calc(100vh-96px)] sticky top-[84px] flex-shrink-0"
      :class="!showLeftPanel ? '!w-full' : ''"
    >
      <PreviewArtifact :report-id="artifactReport.reportId" />
    </div>
  </div>
</template>
